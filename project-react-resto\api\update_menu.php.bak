<?php
require_once 'config.php';

// Enable CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// Handle preflight request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit();
}
$menu = $data['menu'];
$deskripsi = $data['deskripsi'];
$gambar = isset($data['gambar']) ? $data['gambar'] : null;
$harga = $data['harga'];
$kategori_id = $data['kategori_id'];

$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    echo json_encode(['success' => false, 'message' => 'Koneksi database gagal']);
    exit;
}

$stmt = $conn->prepare("UPDATE menus SET menu = ?, deskripsi = ?, gambar = ?, harga = ?, kategori_id = ?, updated_at = NOW() WHERE id = ?");
$stmt->bind_param("sssdii", $menu, $deskripsi, $gambar, $harga, $kategori_id, $id);
if ($stmt->execute()) {
    echo json_encode(['success' => true, 'message' => 'Menu berhasil diupdate']);
} else {
    echo json_encode(['success' => false, 'message' => 'Gagal mengupdate menu: ' . $stmt->error]);
}
$stmt->close();
$conn->close();