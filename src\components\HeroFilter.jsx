import React from 'react';
import { Form, Row, Col, Button, ButtonGroup } from 'react-bootstrap';

const HeroFilter = ({ 
  searchTerm, 
  setSearchTerm, 
  selectedRole, 
  setSelectedRole, 
  sortBy, 
  setSortBy,
  onReset 
}) => {
  const roles = ['All', 'Tank', 'Fighter', 'Assassin', 'Mage', 'Marksman', 'Support'];
  const sortOptions = [
    { value: 'name', label: 'Name' },
    { value: 'power', label: 'Power' },
    { value: 'defense', label: 'Defense' },
    { value: 'speed', label: 'Speed' },
    { value: 'role', label: 'Role' }
  ];

  return (
    <div className="hero-filter-container mb-4 p-3 rounded" style={{ 
      background: 'rgba(255, 255, 255, 0.9)', 
      border: '1px solid rgba(59, 130, 246, 0.3)',
      backdropFilter: 'blur(10px)'
    }}>
      <Row className="g-3 align-items-end">
        <Col md={4}>
          <Form.Label>🔍 Search Heroes</Form.Label>
          <Form.Control
            type="text"
            placeholder="Search by name or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </Col>
        
        <Col md={3}>
          <Form.Label>⚔️ Filter by Role</Form.Label>
          <ButtonGroup className="w-100">
            {roles.map(role => (
              <Button
                key={role}
                variant={selectedRole === role ? 'primary' : 'outline-primary'}
                size="sm"
                onClick={() => setSelectedRole(role)}
                className="text-nowrap"
              >
                {role}
              </Button>
            ))}
          </ButtonGroup>
        </Col>
        
        <Col md={3}>
          <Form.Label>📊 Sort by</Form.Label>
          <Form.Select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
          >
            {sortOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Form.Select>
        </Col>
        
        <Col md={2}>
          <Button 
            variant="outline-secondary" 
            className="w-100"
            onClick={onReset}
          >
            🔄 Reset
          </Button>
        </Col>
      </Row>
    </div>
  );
};

export default HeroFilter;
