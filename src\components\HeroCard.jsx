import React from 'react';
import { <PERSON>, Badge, Button } from 'react-bootstrap';

const HeroCard = ({ hero, onSelect, onEdit, onDelete, isAdmin = false }) => {
  const getRoleBadgeVariant = (role) => {
    const roleVariants = {
      'Tank': 'secondary',
      'Fighter': 'danger',
      'Assassin': 'dark',
      'Mage': 'primary',
      'Marksman': 'warning',
      'Support': 'success'
    };
    return roleVariants[role] || 'info';
  };

  const getHeroImage = (heroName) => {
    // Default hero images - in real app, these would come from API
    const heroImages = {
      'Alucard': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop',
      'Miya': 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=300&h=200&fit=crop',
      'Tigreal': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop',
      'Gusion': 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=300&h=200&fit=crop',
      'Eudora': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop',
      'Rafaela': 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=300&h=200&fit=crop'
    };
    return heroImages[heroName] || 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop';
  };

  return (
    <Card className="hero-card h-100">
      <div className="position-relative">
        <Card.Img 
          variant="top" 
          src={getHeroImage(hero.name)} 
          style={{ height: '200px', objectFit: 'cover' }}
          alt={hero.name}
        />
        <Badge 
          bg={getRoleBadgeVariant(hero.role)} 
          className="position-absolute top-0 end-0 m-2"
        >
          {hero.role}
        </Badge>
        {hero.isLegendary && (
          <Badge 
            bg="warning" 
            className="position-absolute top-0 start-0 m-2 legendary-text"
          >
            ⭐ Legendary
          </Badge>
        )}
      </div>
      
      <Card.Body className="d-flex flex-column">
        <Card.Title className="hero-glow">{hero.name}</Card.Title>
        <Card.Text className="text-muted flex-grow-1">
          {hero.description}
        </Card.Text>
        
        <div className="hero-stats mb-3">
          <div className="row text-center">
            <div className="col-4">
              <small className="text-muted">Power</small>
              <div className="fw-bold text-primary">{hero.power || 85}</div>
            </div>
            <div className="col-4">
              <small className="text-muted">Defense</small>
              <div className="fw-bold text-success">{hero.defense || 70}</div>
            </div>
            <div className="col-4">
              <small className="text-muted">Speed</small>
              <div className="fw-bold text-warning">{hero.speed || 75}</div>
            </div>
          </div>
        </div>

        <div className="hero-skills mb-3">
          <small className="text-muted">Skills:</small>
          <div className="d-flex flex-wrap gap-1 mt-1">
            {(hero.skills || ['Basic Attack', 'Ultimate']).map((skill, index) => (
              <Badge key={index} bg="outline-primary" className="skill-badge">
                {skill}
              </Badge>
            ))}
          </div>
        </div>
        
        <div className="d-flex gap-2 mt-auto">
          <Button 
            variant="primary" 
            size="sm" 
            className="flex-grow-1"
            onClick={() => onSelect && onSelect(hero)}
          >
            🎮 Select Hero
          </Button>
          {isAdmin && (
            <>
              <Button 
                variant="outline-secondary" 
                size="sm"
                onClick={() => onEdit && onEdit(hero)}
              >
                ✏️
              </Button>
              <Button 
                variant="outline-danger" 
                size="sm"
                onClick={() => onDelete && onDelete(hero)}
              >
                🗑️
              </Button>
            </>
          )}
        </div>
      </Card.Body>
    </Card>
  );
};

export default HeroCard;
