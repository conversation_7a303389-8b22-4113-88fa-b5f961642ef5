import React, { useState, useEffect } from 'react';

const PelangganPage = () => {
  const [pelanggan, setPelanggan] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetch("/api/get_pelanggan.php")
      .then((res) => res.json())
      .then((data) => {
        if (data.success) {
          setPelanggan(data.data);
        } else {
          setError(data.message || "Gagal mengambil data pelanggan");
        }
      })
      .catch((err) => {
        setError("Gagal mengambil data pelanggan: " + err.message);
      })
      .finally(() => setLoading(false));
  }, []);

  if (loading) {
    return <div className="text-center mt-5"><span>Loading...</span></div>;
  }
  if (error) {
    return <div className="alert alert-danger mt-4">{error}</div>;
  }

  return (
    <div className="p-4">
      <div className="content-card">
        <h2 className="mb-4">👤 Player Database 👤</h2>
        <p className="mb-4">
          Manage player profiles and battle statistics in the Mobile Legends arena.
        </p>
        <button className="btn-custom btn-primary-custom">🎮 Add New Player</button>
        <div className="mt-4">
          {pelanggan.length === 0 ? (
            <div>No players registered yet.</div>
          ) : (
            <table className="table table-bordered">
              <thead>
                <tr>
                  <th>No</th>
                  <th>Player Name</th>
                  <th>Email</th>
                  <th>Phone</th>
                </tr>
              </thead>
              <tbody>
                {pelanggan.map((item, idx) => (
                  <tr key={item.id}>
                    <td>{idx + 1}</td>
                    <td>{item.nama}</td>
                    <td>{item.email}</td>
                    <td>{item.telepon}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

export default PelangganPage;
