/* Menu Page Styles - Modern Glass Morphism Design */

.menu-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: calc(100vh - 100px);
  position: relative;
  overflow-x: hidden;
  margin: -2rem;
  padding: 2rem;
}

.menu-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Hero Section */
.menu-hero {
  padding: 100px 0;
  position: relative;
  z-index: 1;
}

.hero-content {
  color: white;
  text-align: left;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.text-gradient {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: #f093fb;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.hero-image img {
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  transition: transform 0.5s ease;
}

.hero-image img:hover {
  transform: scale(1.05) rotate(2deg);
}

/* Search and Filter */
.search-bar {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 50px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.search-input {
  background: transparent;
  border: none;
  color: white;
  padding: 15px 25px;
  font-size: 1.1rem;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
  background: transparent;
  border: none;
  box-shadow: none;
  color: white;
}

.search-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  padding: 15px 20px;
  color: white;
  font-size: 1.2rem;
}

.sort-select {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  color: white;
  padding: 15px;
}

.sort-select option {
  background: #667eea;
  color: white;
}

/* Category Filter */
.category-filter {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 2rem;
}

.category-btn {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 15px 25px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  text-decoration: none;
}

.category-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  color: white;
}

.category-btn.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-color: transparent;
  box-shadow: 0 10px 30px rgba(240, 147, 251, 0.4);
}

.category-icon {
  font-size: 1.5rem;
}

.category-name {
  font-size: 1rem;
}

/* Menu Cards */
.menu-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  overflow: hidden;
  transition: all 0.4s ease;
  cursor: pointer;
  height: 100%;
}

.menu-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.15);
}

.menu-card-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.menu-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.menu-card:hover .menu-card-image img {
  transform: scale(1.1);
}

.menu-card-badges {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 2;
}

.badge-new {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  border: none;
  padding: 8px 12px;
  border-radius: 20px;
  font-weight: 600;
  margin-left: 5px;
}

.badge-popular {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
  padding: 8px 12px;
  border-radius: 20px;
  font-weight: 600;
  margin-left: 5px;
}

.menu-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.menu-card:hover .menu-card-overlay {
  opacity: 1;
}

.btn-view-details {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 25px;
  padding: 12px 25px;
  font-weight: 600;
  color: #667eea;
  transition: all 0.3s ease;
}

.btn-view-details:hover {
  background: white;
  transform: scale(1.05);
}

.menu-card-body {
  padding: 25px;
  color: white;
}

.menu-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.menu-card-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0;
  color: white;
}

.menu-card-rating {
  display: flex;
  align-items: center;
  gap: 5px;
  background: rgba(255, 255, 255, 0.1);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.9rem;
}

.rating-stars {
  font-size: 1rem;
}

.menu-card-description {
  font-size: 0.95rem;
  opacity: 0.9;
  margin-bottom: 20px;
  line-height: 1.5;
}

.menu-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.menu-card-price {
  font-size: 1.4rem;
  font-weight: 700;
  color: #f093fb;
}

.btn-add-cart {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-add-cart:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
}

.menu-card-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  opacity: 0.8;
}

/* Floating Cart */
.floating-cart {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

.cart-toggle {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
  border-radius: 25px;
  padding: 15px 25px;
  font-weight: 600;
  font-size: 1.1rem;
  box-shadow: 0 10px 30px rgba(240, 147, 251, 0.4);
  transition: all 0.3s ease;
}

.cart-toggle:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(240, 147, 251, 0.6);
}

/* Modal Styles */
.modal-header-custom {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 15px 15px 0 0;
}

.modal-body-custom {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 0 0 15px 15px;
}

.item-details {
  padding: 20px 0;
}

.item-badges .badge {
  margin-right: 10px;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 600;
}

.item-description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 20px;
  color: #2d3748;
}

.item-meta {
  background: rgba(102, 126, 234, 0.1);
  padding: 15px;
  border-radius: 15px;
  margin-bottom: 20px;
}

.meta-item {
  margin-bottom: 8px;
  color: #4a5568;
}

.ingredients-list, .allergens-list {
  margin-top: 10px;
}

.item-price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 15px;
  margin-top: 20px;
}

.item-price {
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
}

/* No Results */
.no-results {
  color: white;
  padding: 60px 20px;
}

.no-results h3 {
  font-size: 2rem;
  margin-bottom: 15px;
}

.no-results p {
  font-size: 1.1rem;
  opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-stats {
    gap: 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .category-filter {
    gap: 0.5rem;
  }

  .category-btn {
    padding: 10px 15px;
    font-size: 0.9rem;
  }

  .menu-card-image {
    height: 200px;
  }

  .floating-cart {
    bottom: 20px;
    right: 20px;
  }

  .cart-toggle {
    padding: 12px 20px;
    font-size: 1rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scale-in {
  animation: scaleIn 0.4s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Glass morphism effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
