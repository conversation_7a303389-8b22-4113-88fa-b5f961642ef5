/* Mobile Legends Theme CSS */

/* Hero Card Animations */
.hero-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(51, 65, 85, 0.9) 100%);
  border: 2px solid rgba(251, 191, 36, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.hero-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.2), transparent);
  transition: 0.8s;
}

.hero-card:hover::before {
  left: 100%;
}

.hero-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(251, 191, 36, 0.6);
  box-shadow: 
    0 20px 40px rgba(30, 58, 138, 0.3),
    0 0 30px rgba(251, 191, 36, 0.4);
}

/* <PERSON> Styles */
.battle-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.battle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: 0.6s;
}

.battle-btn:hover::before {
  left: 100%;
}

.battle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(239, 68, 68, 0.4);
}

/* Skill Card Styles */
.skill-card {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  padding: 1rem;
  margin: 0.5rem 0;
  transition: all 0.3s ease;
}

.skill-card:hover {
  border-color: rgba(16, 185, 129, 0.6);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.2) 100%);
  transform: translateX(5px);
}

/* Player Stats */
.player-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.stat-item {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  border-color: rgba(139, 92, 246, 0.6);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.2);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  color: #94a3b8;
  font-weight: 500;
  margin-top: 0.5rem;
}

/* Hero Role Badges */
.role-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-tank {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
}

.role-fighter {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.role-assassin {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.role-mage {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.role-marksman {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.role-support {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

/* Battle Arena Background */
.battle-arena {
  background: 
    radial-gradient(circle at 25% 25%, rgba(239, 68, 68, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  min-height: 100vh;
  position: relative;
}

.battle-arena::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(251, 191, 36, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  animation: battleGlow 8s ease-in-out infinite alternate;
}

@keyframes battleGlow {
  0% { opacity: 0.3; }
  100% { opacity: 0.7; }
}

/* Legendary Text Effect */
.legendary-text {
  background: linear-gradient(45deg, #fbbf24, #f59e0b, #ef4444, #8b5cf6, #3b82f6);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: legendaryShine 3s ease-in-out infinite;
  font-weight: 800;
  text-shadow: 0 0 30px rgba(251, 191, 36, 0.5);
}

@keyframes legendaryShine {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Mobile Legends Loading Animation */
.ml-loading {
  display: inline-block;
  width: 60px;
  height: 60px;
  border: 3px solid rgba(251, 191, 36, 0.3);
  border-radius: 50%;
  border-top-color: #fbbf24;
  animation: mlSpin 1s ease-in-out infinite;
}

@keyframes mlSpin {
  to { transform: rotate(360deg); }
}

/* Hero Selection Grid */
.hero-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 2rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .player-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-number {
    font-size: 2rem;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}
