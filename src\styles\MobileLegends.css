/* Mobile Legends Theme CSS */

/* Hero Card Animations */
.hero-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

.hero-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.2), transparent);
  transition: 0.8s;
}

.hero-card:hover::before {
  left: 100%;
}

.hero-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(251, 191, 36, 0.6);
  box-shadow:
    0 20px 40px rgba(30, 58, 138, 0.3),
    0 0 30px rgba(251, 191, 36, 0.4);
}

/* Battle <PERSON>ton Styles */
.battle-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.battle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: 0.6s;
}

.battle-btn:hover::before {
  left: 100%;
}

.battle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(239, 68, 68, 0.4);
}

/* Skill Card Styles */
.skill-card {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  padding: 1rem;
  margin: 0.5rem 0;
  transition: all 0.3s ease;
}

.skill-card:hover {
  border-color: rgba(16, 185, 129, 0.6);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.2) 100%);
  transform: translateX(5px);
}

/* Player Stats */
.player-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.stat-item {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  border-color: rgba(139, 92, 246, 0.6);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.2);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  color: #94a3b8;
  font-weight: 500;
  margin-top: 0.5rem;
}

/* Hero Role Badges */
.role-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-tank {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
}

.role-fighter {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.role-assassin {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.role-mage {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.role-marksman {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.role-support {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

/* Battle Arena Background */
.battle-arena {
  background:
    radial-gradient(circle at 25% 25%, rgba(239, 68, 68, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  min-height: 100vh;
  position: relative;
}

.battle-arena::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(251, 191, 36, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  animation: battleGlow 8s ease-in-out infinite alternate;
}

@keyframes battleGlow {
  0% { opacity: 0.3; }
  100% { opacity: 0.7; }
}

/* Legendary Text Effect */
.legendary-text {
  background: linear-gradient(45deg, #fbbf24, #f59e0b, #ef4444, #8b5cf6, #3b82f6);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: legendaryShine 3s ease-in-out infinite;
  font-weight: 800;
  text-shadow: 0 0 30px rgba(251, 191, 36, 0.5);
}

@keyframes legendaryShine {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Mobile Legends Loading Animation */
.ml-loading {
  display: inline-block;
  width: 60px;
  height: 60px;
  border: 3px solid rgba(251, 191, 36, 0.3);
  border-radius: 50%;
  border-top-color: #fbbf24;
  animation: mlSpin 1s ease-in-out infinite;
}

@keyframes mlSpin {
  to { transform: rotate(360deg); }
}

/* Hero Selection Grid */
.hero-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 2rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .player-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .stat-number {
    font-size: 2rem;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* Content Card Enhancement */
.content-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  box-shadow:
    0 10px 30px rgba(59, 130, 246, 0.15),
    0 0 20px rgba(251, 191, 36, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.content-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
  pointer-events: none;
}

.content-card:hover {
  transform: translateY(-2px);
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow:
    0 15px 40px rgba(30, 58, 138, 0.3),
    0 0 30px rgba(251, 191, 36, 0.2);
}

/* Table Enhancement */
.table {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

.table thead th {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
  color: white;
  border: none;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 1rem;
}

.table tbody td {
  background: rgba(248, 250, 252, 0.8);
  color: #1e293b;
  border-color: rgba(59, 130, 246, 0.2);
  padding: 1rem;
  vertical-align: middle;
}

.table tbody tr:hover td {
  background: rgba(59, 130, 246, 0.1);
  color: #1e293b;
}

/* Button Enhancements */
.btn-custom.btn-primary-custom {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  border: 2px solid rgba(251, 191, 36, 0.3);
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.btn-custom.btn-primary-custom:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  border-color: rgba(251, 191, 36, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(30, 58, 138, 0.4);
}

/* Alert Enhancement */
.alert {
  border-radius: 12px;
  border: none;
  backdrop-filter: blur(10px);
  font-weight: 500;
}

.alert-success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.9) 0%, rgba(5, 150, 105, 0.9) 100%);
  color: white;
}

.alert-danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
  color: white;
}

.alert-info {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(37, 99, 235, 0.9) 100%);
  color: white;
}

/* Form Enhancement */
.form-control {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  color: #1e293b;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(59, 130, 246, 0.6);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
  color: #1e293b;
}

.form-control::placeholder {
  color: #64748b;
}

.form-label {
  color: #1e293b;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

/* Modal Enhancement */
.modal-content {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(15px);
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
}

.modal-header {
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
  color: white;
  border-radius: 16px 16px 0 0;
}

.modal-title {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modal-footer {
  border-top: 1px solid rgba(59, 130, 246, 0.3);
  background: rgba(248, 250, 252, 0.8);
  border-radius: 0 0 16px 16px;
}

.modal-body {
  color: #1e293b;
}

/* Spinner Enhancement */
.spinner-border {
  color: #fbbf24;
  width: 3rem;
  height: 3rem;
  border-width: 0.3em;
}

/* Card Enhancement */
.card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

.card:hover {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.card-body {
  color: #1e293b;
}

/* Text Colors */
.text-muted {
  color: #64748b !important;
}

.text-white {
  color: #ffffff !important;
}

.text-dark {
  color: #1e293b !important;
}

/* Hero Glow Effect */
.hero-glow {
  animation: heroGlow 2s ease-in-out infinite alternate;
}

@keyframes heroGlow {
  from {
    text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
  }
  to {
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.8), 0 0 30px rgba(59, 130, 246, 0.5);
  }
}

/* Additional Component Styles */
.hero-selection-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.hero-selection-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.hero-selection-item {
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.hero-selection-item:hover {
  transform: scale(1.05);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.player-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.player-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.player-stats-card {
  border: 1px solid rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
}

/* Skill Badge */
.skill-badge {
  background: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Hero Filter Container */
.hero-filter-container {
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
}

/* Battle Arena Enhancements */
.battle-arena {
  background:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(251, 191, 36, 0.05) 0%, transparent 50%),
    linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(255, 255, 255, 0.95) 100%);
  min-height: 100vh;
  position: relative;
  border-radius: 16px;
  padding: 2rem;
}

.battle-arena::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(251, 191, 36, 0.03) 0%, transparent 50%);
  animation: battleGlow 8s ease-in-out infinite alternate;
  border-radius: 16px;
}

/* Loading Animation for Bright Theme */
.ml-loading {
  display: inline-block;
  width: 60px;
  height: 60px;
  border: 3px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: mlSpin 1s ease-in-out infinite;
}

/* Menu Title Enhancement */
.menu-title {
  background: linear-gradient(45deg, #3b82f6, #60a5fa, #fbbf24, #f59e0b);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: legendaryShine 3s ease-in-out infinite;
  font-weight: 800;
  font-size: 2.5rem;
}

/* Button Hover Effects */
.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.btn-success:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.btn-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.btn-warning:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);
}

/* Progress Bar Enhancement */
.progress {
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 10px;
  overflow: hidden;
}

.progress-bar {
  border-radius: 10px;
  transition: all 0.3s ease;
}

/* Badge Enhancements */
.badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  padding: 0.5rem 0.75rem;
}

/* Navigation Enhancements */
.nav-header {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
  color: white;
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Responsive Improvements */
@media (max-width: 768px) {
  .menu-title {
    font-size: 2rem;
  }

  .battle-arena {
    padding: 1rem;
  }

  .hero-filter-container .btn-group {
    flex-wrap: wrap;
  }

  .hero-filter-container .btn-group .btn {
    margin-bottom: 0.5rem;
  }
}
