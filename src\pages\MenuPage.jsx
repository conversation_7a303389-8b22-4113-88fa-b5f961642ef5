import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Button, Alert } from 'react-bootstrap';
import HeroCard from '../components/HeroCard';
import HeroFilter from '../components/HeroFilter';
import HeroModal from '../components/HeroModal';
import { getHeroes, addHero, updateHero, deleteHero, searchHeroes, sortHeroes } from '../data/heroesData';
import { isAdmin } from '../utils/auth';

const MenuPage = () => {
  const [heroes, setHeroes] = useState([]);
  const [filteredHeroes, setFilteredHeroes] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState('All');
  const [sortBy, setSortBy] = useState('name');
  const [showModal, setShowModal] = useState(false);
  const [selectedHero, setSelectedHero] = useState(null);
  const [isEdit, setIsEdit] = useState(false);
  const [alert, setAlert] = useState({ show: false, message: '', variant: 'success' });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadHeroes();
  }, []);

  useEffect(() => {
    filterAndSortHeroes();
  }, [heroes, searchTerm, selectedRole, sortBy]);

  const loadHeroes = () => {
    try {
      const heroesData = getHeroes();
      setHeroes(heroesData);
      setLoading(false);
    } catch (error) {
      console.error('Error loading heroes:', error);
      showAlert('Error loading heroes', 'danger');
      setLoading(false);
    }
  };

  const filterAndSortHeroes = () => {
    let filtered = heroes;

    // Apply search filter
    if (searchTerm) {
      filtered = searchHeroes(searchTerm);
    }

    // Apply role filter
    if (selectedRole !== 'All') {
      filtered = filtered.filter(hero => hero.role === selectedRole);
    }

    // Apply sorting
    filtered = sortHeroes(filtered, sortBy);

    setFilteredHeroes(filtered);
  };

  const showAlert = (message, variant = 'success') => {
    setAlert({ show: true, message, variant });
    setTimeout(() => setAlert({ show: false, message: '', variant: 'success' }), 3000);
  };

  const handleAddHero = () => {
    setSelectedHero(null);
    setIsEdit(false);
    setShowModal(true);
  };

  const handleEditHero = (hero) => {
    setSelectedHero(hero);
    setIsEdit(true);
    setShowModal(true);
  };

  const handleDeleteHero = (hero) => {
    if (window.confirm(`Are you sure you want to delete ${hero.name}?`)) {
      try {
        deleteHero(hero.id);
        loadHeroes();
        showAlert(`${hero.name} has been deleted successfully`, 'success');
      } catch (error) {
        console.error('Error deleting hero:', error);
        showAlert('Error deleting hero', 'danger');
      }
    }
  };

  const handleSaveHero = async (heroData) => {
    try {
      if (isEdit) {
        updateHero({ ...heroData, id: selectedHero.id });
        showAlert(`${heroData.name} has been updated successfully`, 'success');
      } else {
        addHero(heroData);
        showAlert(`${heroData.name} has been added successfully`, 'success');
      }
      loadHeroes();
    } catch (error) {
      console.error('Error saving hero:', error);
      showAlert('Error saving hero', 'danger');
      throw error;
    }
  };

  const handleSelectHero = (hero) => {
    showAlert(`${hero.name} selected! Ready for battle!`, 'info');
  };

  const handleResetFilters = () => {
    setSearchTerm('');
    setSelectedRole('All');
    setSortBy('name');
  };

  if (loading) {
    return (
      <div className="p-4 text-center">
        <div className="ml-loading mx-auto mb-3"></div>
        <p>Loading heroes...</p>
      </div>
    );
  }

  return (
    <div className="p-4">
      <Container fluid>
        <div className="text-center mb-5">
          <h2 className="menu-title legendary-text">🔥 Hero Skills & Abilities 🔥</h2>
          <p className="text-muted">Choose your champion and master their legendary abilities</p>
        </div>

        {alert.show && (
          <Alert variant={alert.variant} onClose={() => setAlert({...alert, show: false})} dismissible>
            {alert.message}
          </Alert>
        )}

        <div className="d-flex justify-content-between align-items-center mb-4">
          <h4>⚔️ Available Heroes ({filteredHeroes.length})</h4>
          {isAdmin() && (
            <Button variant="primary" onClick={handleAddHero}>
              🎮 Add New Hero
            </Button>
          )}
        </div>

        <HeroFilter
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          selectedRole={selectedRole}
          setSelectedRole={setSelectedRole}
          sortBy={sortBy}
          setSortBy={setSortBy}
          onReset={handleResetFilters}
        />

        {filteredHeroes.length === 0 ? (
          <div className="text-center py-5">
            <h5 className="text-muted">No heroes found</h5>
            <p className="text-muted">Try adjusting your search criteria</p>
          </div>
        ) : (
          <Row className="hero-grid">
            {filteredHeroes.map(hero => (
              <Col key={hero.id} lg={4} md={6} className="mb-4">
                <HeroCard
                  hero={hero}
                  onSelect={handleSelectHero}
                  onEdit={handleEditHero}
                  onDelete={handleDeleteHero}
                  isAdmin={isAdmin()}
                />
              </Col>
            ))}
          </Row>
        )}

        <HeroModal
          show={showModal}
          onHide={() => setShowModal(false)}
          hero={selectedHero}
          onSave={handleSaveHero}
          isEdit={isEdit}
        />
      </Container>
    </div>
  );
};

export default MenuPage;
